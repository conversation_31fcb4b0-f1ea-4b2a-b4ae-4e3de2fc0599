# 🚀 Oracle Voice Chat - Production Deployment Summary

## 📦 Pripravené súbory pre nasadenie

### Backend (chat-backend/)
- ✅ `Dockerfile` - Multi-stage production build
- ✅ `docker-compose.yml` - Complete stack (app + nginx + redis)
- ✅ `.env` - Production environment variables
- ✅ `nginx/` - SSL proxy configuration
- ✅ `deploy-oracle.sh` - Automated deployment script
- ✅ `DEPLOYMENT.md` - Detailed deployment guide
- ✅ `oracle-voice-chat-backend.tar.gz` - Ready for upload

### Frontend (cloudflare-clean/)
- ✅ `index.html` - Updated for production
- ✅ `package.json` - Build scripts
- ✅ `inject-env.js` - Environment variables injection
- ✅ `_headers` - Security and caching headers
- ✅ `_redirects` - SPA routing
- ✅ `DEPLOYMENT-GUIDE.md` - Complete deployment guide

## 🎯 Nasadenie krok za krokom

### 1. Backend na Oracle Cloud (15 minút)

```bash
# 1. Pripojenie na Oracle VM
ssh opc@YOUR_ORACLE_VM_IP

# 2. Vytvorenie adresára
sudo mkdir -p /opt/oracle-voice-chat
sudo chown opc:opc /opt/oracle-voice-chat
cd /opt/oracle-voice-chat

# 3. Nahratie súborov (SCP z lokálneho počítača)
scp oracle-voice-chat-backend.tar.gz opc@YOUR_ORACLE_VM_IP:/opt/oracle-voice-chat/

# 4. Rozbalenie a konfigurácia
tar -xzf oracle-voice-chat-backend.tar.gz
nano .env  # Nastavte API kľúče

# 5. SSL certifikáty (Cloudflare Origin Certificate)
mkdir -p ssl
nano ssl/origin.crt  # Vložte certificate
nano ssl/origin.key  # Vložte private key

# 6. Spustenie
sudo ./deploy-oracle.sh
```

### 2. Frontend na Cloudflare Pages (10 minút)

```bash
# 1. GitHub repozitár
cd cloudflare-clean
git init
git add .
git commit -m "Oracle Voice Chat Frontend"
git remote add origin https://github.com/YOUR_USERNAME/oracle-voice-chat-frontend.git
git push -u origin main

# 2. Cloudflare Pages
# - Choďte na dash.cloudflare.com
# - Pages → Create project → Connect to Git
# - Vyberte repozitár
# - Build command: npm run build
# - Environment variables:
#   ORACLE_BACKEND=https://your-backend-domain.com
#   WS_URL=wss://your-backend-domain.com
```

### 3. DNS konfigurácia (5 minút)

```
# Cloudflare DNS záznamy:
A record: api.yourdomain.com → YOUR_ORACLE_VM_IP
CNAME record: chat.yourdomain.com → oracle-voice-chat.pages.dev
```

## 🔑 Potrebné API kľúče

### Deepgram (Speech-to-Text)
1. Registrácia: https://console.deepgram.com/
2. Vytvorte API kľúč
3. Pridajte do `.env`: `DEEPGRAM_API_KEY=your_key`

### OpenAI (GPT-4 Chat)
1. Registrácia: https://platform.openai.com/
2. Vytvorte API kľúč
3. Pridajte do `.env`: `OPENAI_API_KEY=your_key`

### Cloudflare Origin Certificate
1. Cloudflare Dashboard → SSL/TLS → Origin Server
2. Create Certificate
3. Uložte do `ssl/origin.crt` a `ssl/origin.key`

## 🧪 Testovanie po nasadení

### Backend testy
```bash
# Health check
curl https://api.yourdomain.com/health

# API endpoints
curl https://api.yourdomain.com/api/deepgram/status
curl https://api.yourdomain.com/api/chat/status
curl https://api.yourdomain.com/api/tts/status
```

### Frontend test
1. Otvorte `https://chat.yourdomain.com`
2. Kliknite "Aktivovať Voice Chat"
3. Testujte voice funkcionalitu

## 📊 Očakávané výsledky

### Funkčná aplikácia s:
- ✅ **WebSocket spojenie** - Real-time komunikácia
- ✅ **Voice-to-Text** - Deepgram STT (alebo mock)
- ✅ **AI Chat** - OpenAI GPT-4 (alebo mock)
- ✅ **Text-to-Speech** - Piper TTS (alebo mock)
- ✅ **SSL/HTTPS** - Bezpečné spojenie
- ✅ **CORS** - Správne nakonfigurované
- ✅ **Monitoring** - Health checks a metriky

### Performance metriky:
- **Latencia STT**: < 500ms (s reálnym API)
- **Latencia AI**: 1-3s (GPT-4)
- **Latencia TTS**: 1-2s (Piper TTS)
- **WebSocket ping**: < 100ms

## 🔧 Údržba a monitoring

### Užitočné príkazy
```bash
# Backend status
docker-compose ps
docker-compose logs -f

# Reštart služieb
docker-compose restart

# Aktualizácia
git pull
docker-compose up -d --build
```

### Monitoring endpoints
- Backend health: `https://api.yourdomain.com/health`
- Metriky: `https://api.yourdomain.com/api/metrics`
- WebSocket test: `https://api.yourdomain.com/websocket-test.html`

## 🚨 Riešenie problémov

### Časté problémy:
1. **WebSocket spojenie zlyhá** → Skontrolujte CORS a SSL
2. **API volania zlyhávajú** → Skontrolujte environment variables
3. **Audio nefunguje** → Skontrolujte HTTPS a mikrofón permissions

### Debug kroky:
1. Skontrolujte browser console
2. Skontrolujte backend logy: `docker-compose logs`
3. Testujte API endpoints jednotlivne
4. Overte DNS a SSL konfiguráciu

## 🎉 Hotovo!

Po dokončení týchto krokov budete mať plne funkčnú Oracle Voice Chat aplikáciu bežiacu v produkcii:

- **Frontend**: `https://chat.yourdomain.com`
- **Backend**: `https://api.yourdomain.com`
- **WebSocket**: `wss://api.yourdomain.com/ws`

Aplikácia podporuje:
- Real-time voice chat
- AI konverzácie
- Text-to-speech odpovede
- Bezpečnú HTTPS komunikáciu
- Automatické reconnect
- Error handling a fallbacks

**Gratulujeme k úspešnému nasadeniu! 🎊**
