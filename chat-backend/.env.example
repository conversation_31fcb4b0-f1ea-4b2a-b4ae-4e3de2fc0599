# Oracle Voice Chat Backend - Environment Variables

# Server Configuration
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
TZ=Europe/Bratislava

# API Keys (nahraď skutočnými kľúčmi)
DEEPGRAM_API_KEY=your-deepgram-api-key-here
OPENAI_API_KEY=your-openai-api-key-here

# Redis Configuration
USE_REDIS=false
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=oracleVoiceChat2024

# Upstash Redis (alternatíva)
UPSTASH_REDIS_REST_URL=
UPSTASH_REDIS_REST_TOKEN=

# CORS Configuration
ALLOWED_ORIGINS=https://oracle-voice-chat.pages.dev,https://chat.hotovo.ai,http://localhost:3000

# SSL/TLS Configuration
SSL_CERT_PATH=/etc/ssl/cloudflare/origin-cert.pem
SSL_KEY_PATH=/etc/ssl/cloudflare/origin-cert.key

# Logging
LOG_LEVEL=info
LOG_FILE=logs/voice-chat.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_AUDIO_FILE_SIZE=50MB
MAX_AUDIO_DURATION=300

# TTS Configuration
TTS_VOICE=default
TTS_SPEED=1.0
TTS_CACHE_ENABLED=true
TTS_CACHE_TTL=3600

# Session Configuration
SESSION_MAX_AGE=86400000
SESSION_CLEANUP_INTERVAL=3600000

# Monitoring
ENABLE_METRICS=true
METRICS_ENDPOINT=/api/metrics

# Watchtower (Docker auto-updates)
WATCHTOWER_EMAIL_FROM=<EMAIL>
WATCHTOWER_EMAIL_TO=<EMAIL>

# Development
DEBUG=voice-chat:*
MOCK_SERVICES=true
