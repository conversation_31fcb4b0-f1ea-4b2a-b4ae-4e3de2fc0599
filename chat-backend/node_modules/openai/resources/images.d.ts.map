{"version": 3, "file": "images.d.ts", "sourceRoot": "", "sources": ["../src/resources/images.ts"], "names": [], "mappings": "OAEO,EAAE,WAAW,EAAE;OACf,KAAK,SAAS;OACd,EAAE,UAAU,EAAE;OACd,EAAE,MAAM,EAAE;OACV,EAAE,KAAK,UAAU,EAAE;OACnB,EAAE,cAAc,EAAE;AAGzB,qBAAa,MAAO,SAAQ,WAAW;IACrC;;;;;;;;;OASG;IACH,eAAe,CAAC,IAAI,EAAE,0BAA0B,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;IAOvG;;;;;;;;;;;OAWG;IACH,IAAI,CAAC,IAAI,EAAE,2BAA2B,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;IAC7F,IAAI,CAAC,IAAI,EAAE,wBAAwB,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;IACxG,IAAI,CACF,IAAI,EAAE,mBAAmB,EACzB,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,cAAc,CAAC;IAW5D;;;;;;;;;;OAUG;IACH,QAAQ,CAAC,IAAI,EAAE,+BAA+B,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;IACrG,QAAQ,CACN,IAAI,EAAE,4BAA4B,EAClC,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IAC1C,QAAQ,CACN,IAAI,EAAE,uBAAuB,EAC7B,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,cAAc,CAAC;CAS5D;AAED;;GAEG;AACH,MAAM,WAAW,KAAK;IACpB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;;OAIG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AAED;;GAEG;AACH,MAAM,WAAW,uBAAuB;IACtC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,UAAU,EAAE,aAAa,GAAG,QAAQ,GAAG,MAAM,CAAC;IAE9C;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,aAAa,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;IAEvC;;OAEG;IACH,OAAO,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;IAE5C;;OAEG;IACH,IAAI,EAAE,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,MAAM,CAAC;IAEvD;;OAEG;IACH,IAAI,EAAE,sBAAsB,CAAC;IAE7B;;OAEG;IACH,KAAK,EAAE,uBAAuB,CAAC,KAAK,CAAC;CACtC;AAED,yBAAiB,uBAAuB,CAAC;IACvC;;OAEG;IACH,UAAiB,KAAK;QACpB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,oBAAoB,EAAE,KAAK,CAAC,kBAAkB,CAAC;QAE/C;;WAEG;QACH,aAAa,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;KACtB;IAED,UAAiB,KAAK,CAAC;QACrB;;WAEG;QACH,UAAiB,kBAAkB;YACjC;;eAEG;YACH,YAAY,EAAE,MAAM,CAAC;YAErB;;eAEG;YACH,WAAW,EAAE,MAAM,CAAC;SACrB;KACF;CACF;AAED;;GAEG;AACH,MAAM,WAAW,0BAA0B;IACzC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,UAAU,EAAE,aAAa,GAAG,QAAQ,GAAG,MAAM,CAAC;IAE9C;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,aAAa,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;IAEvC;;OAEG;IACH,mBAAmB,EAAE,MAAM,CAAC;IAE5B;;OAEG;IACH,OAAO,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;IAE5C;;OAEG;IACH,IAAI,EAAE,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,MAAM,CAAC;IAEvD;;OAEG;IACH,IAAI,EAAE,0BAA0B,CAAC;CAClC;AAED;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,0BAA0B,GAAG,uBAAuB,CAAC;AAExF;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,UAAU,EAAE,aAAa,GAAG,QAAQ,GAAG,MAAM,CAAC;IAE9C;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,aAAa,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;IAEvC;;OAEG;IACH,OAAO,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;IAE5C;;OAEG;IACH,IAAI,EAAE,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,MAAM,CAAC;IAEvD;;OAEG;IACH,IAAI,EAAE,4BAA4B,CAAC;IAEnC;;OAEG;IACH,KAAK,EAAE,sBAAsB,CAAC,KAAK,CAAC;CACrC;AAED,yBAAiB,sBAAsB,CAAC;IACtC;;OAEG;IACH,UAAiB,KAAK;QACpB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,oBAAoB,EAAE,KAAK,CAAC,kBAAkB,CAAC;QAE/C;;WAEG;QACH,aAAa,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;KACtB;IAED,UAAiB,KAAK,CAAC;QACrB;;WAEG;QACH,UAAiB,kBAAkB;YACjC;;eAEG;YACH,YAAY,EAAE,MAAM,CAAC;YAErB;;eAEG;YACH,WAAW,EAAE,MAAM,CAAC;SACrB;KACF;CACF;AAED;;GAEG;AACH,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,UAAU,EAAE,aAAa,GAAG,QAAQ,GAAG,MAAM,CAAC;IAE9C;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,aAAa,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;IAEvC;;OAEG;IACH,mBAAmB,EAAE,MAAM,CAAC;IAE5B;;OAEG;IACH,OAAO,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;IAE5C;;OAEG;IACH,IAAI,EAAE,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,MAAM,CAAC;IAEvD;;OAEG;IACH,IAAI,EAAE,gCAAgC,CAAC;CACxC;AAED;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG,yBAAyB,GAAG,sBAAsB,CAAC;AAErF,MAAM,MAAM,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,aAAa,CAAC;AAEjE;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;OAGG;IACH,UAAU,CAAC,EAAE,aAAa,GAAG,QAAQ,CAAC;IAEtC;;OAEG;IACH,IAAI,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IAEpB;;OAEG;IACH,aAAa,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;IAExC;;OAEG;IACH,OAAO,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;IAEpC;;;OAGG;IACH,IAAI,CAAC,EAAE,WAAW,GAAG,WAAW,GAAG,WAAW,CAAC;IAE/C;;OAEG;IACH,KAAK,CAAC,EAAE,cAAc,CAAC,KAAK,CAAC;CAC9B;AAED,yBAAiB,cAAc,CAAC;IAC9B;;OAEG;IACH,UAAiB,KAAK;QACpB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,oBAAoB,EAAE,KAAK,CAAC,kBAAkB,CAAC;QAE/C;;WAEG;QACH,aAAa,EAAE,MAAM,CAAC;QAEtB;;WAEG;QACH,YAAY,EAAE,MAAM,CAAC;KACtB;IAED,UAAiB,KAAK,CAAC;QACrB;;WAEG;QACH,UAAiB,kBAAkB;YACjC;;eAEG;YACH,YAAY,EAAE,MAAM,CAAC;YAErB;;eAEG;YACH,WAAW,EAAE,MAAM,CAAC;SACrB;KACF;CACF;AAED,MAAM,WAAW,0BAA0B;IACzC;;;OAGG;IACH,KAAK,EAAE,UAAU,CAAC;IAElB;;;OAGG;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,UAAU,GAAG,IAAI,CAAC;IAE1C;;OAEG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElB;;;;OAIG;IACH,eAAe,CAAC,EAAE,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC;IAE5C;;;OAGG;IACH,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,IAAI,CAAC;IAElD;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,MAAM,eAAe,GAAG,2BAA2B,GAAG,wBAAwB,CAAC;AAErF,MAAM,WAAW,mBAAmB;IAClC;;;;;;;;OAQG;IACH,KAAK,EAAE,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;IAEtC;;;OAGG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;;;;;;;OAQG;IACH,UAAU,CAAC,EAAE,aAAa,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAEtD;;;;OAIG;IACH,cAAc,CAAC,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC;IAEvC;;;;;OAKG;IACH,IAAI,CAAC,EAAE,UAAU,CAAC;IAElB;;;;OAIG;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,UAAU,GAAG,IAAI,CAAC;IAE1C;;OAEG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElB;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEnC;;;;OAIG;IACH,aAAa,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IAE/C;;;;;;;OAOG;IACH,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B;;;;OAIG;IACH,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IAEjE;;;;;OAKG;IACH,eAAe,CAAC,EAAE,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC;IAE5C;;;;OAIG;IACH,IAAI,CAAC,EAAE,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,MAAM,GAAG,IAAI,CAAC;IAEvF;;;;OAIG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAExB;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,yBAAiB,eAAe,CAAC;IAC/B,KAAY,2BAA2B,GAAG,SAAS,CAAC,2BAA2B,CAAC;IAChF,KAAY,wBAAwB,GAAG,SAAS,CAAC,wBAAwB,CAAC;CAC3E;AAED,MAAM,WAAW,2BAA4B,SAAQ,mBAAmB;IACtE;;;;OAIG;IACH,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;CACvB;AAED,MAAM,WAAW,wBAAyB,SAAQ,mBAAmB;IACnE;;;;OAIG;IACH,MAAM,EAAE,IAAI,CAAC;CACd;AAED,MAAM,MAAM,mBAAmB,GAAG,+BAA+B,GAAG,4BAA4B,CAAC;AAEjG,MAAM,WAAW,uBAAuB;IACtC;;;;OAIG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;;;;;;;OAQG;IACH,UAAU,CAAC,EAAE,aAAa,GAAG,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;IAEtD;;;;OAIG;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,UAAU,GAAG,IAAI,CAAC;IAE1C;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,IAAI,CAAC;IAEnC;;;OAGG;IACH,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAElB;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEnC;;;OAGG;IACH,aAAa,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IAE/C;;;;;;;OAOG;IACH,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B;;;;;;;;OAQG;IACH,OAAO,CAAC,EAAE,UAAU,GAAG,IAAI,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;IAExE;;;;;OAKG;IACH,eAAe,CAAC,EAAE,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC;IAE5C;;;;;OAKG;IACH,IAAI,CAAC,EACD,MAAM,GACN,WAAW,GACX,WAAW,GACX,WAAW,GACX,SAAS,GACT,SAAS,GACT,WAAW,GACX,WAAW,GACX,IAAI,CAAC;IAET;;;;OAIG;IACH,MAAM,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;IAExB;;;;;OAKG;IACH,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,IAAI,CAAC;IAEnC;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,yBAAiB,mBAAmB,CAAC;IACnC,KAAY,+BAA+B,GAAG,SAAS,CAAC,+BAA+B,CAAC;IACxF,KAAY,4BAA4B,GAAG,SAAS,CAAC,4BAA4B,CAAC;CACnF;AAED,MAAM,WAAW,+BAAgC,SAAQ,uBAAuB;IAC9E;;;;OAIG;IACH,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;CACvB;AAED,MAAM,WAAW,4BAA6B,SAAQ,uBAAuB;IAC3E;;;;OAIG;IACH,MAAM,EAAE,IAAI,CAAC;CACd;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,OAAO,EACL,KAAK,KAAK,IAAI,KAAK,EACnB,KAAK,uBAAuB,IAAI,uBAAuB,EACvD,KAAK,0BAA0B,IAAI,0BAA0B,EAC7D,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,sBAAsB,IAAI,sBAAsB,EACrD,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,UAAU,IAAI,UAAU,EAC7B,KAAK,cAAc,IAAI,cAAc,EACrC,KAAK,0BAA0B,IAAI,0BAA0B,EAC7D,KAAK,eAAe,IAAI,eAAe,EACvC,KAAK,2BAA2B,IAAI,2BAA2B,EAC/D,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,+BAA+B,IAAI,+BAA+B,EACvE,KAAK,4BAA4B,IAAI,4BAA4B,GAClE,CAAC;CACH"}