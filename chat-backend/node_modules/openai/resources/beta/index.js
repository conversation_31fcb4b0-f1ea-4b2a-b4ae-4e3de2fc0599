"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Threads = exports.Realtime = exports.Beta = exports.Assistants = void 0;
var assistants_1 = require("./assistants.js");
Object.defineProperty(exports, "Assistants", { enumerable: true, get: function () { return assistants_1.Assistants; } });
var beta_1 = require("./beta.js");
Object.defineProperty(exports, "Beta", { enumerable: true, get: function () { return beta_1.Beta; } });
var index_1 = require("./realtime/index.js");
Object.defineProperty(exports, "Realtime", { enumerable: true, get: function () { return index_1.Realtime; } });
var index_2 = require("./threads/index.js");
Object.defineProperty(exports, "Threads", { enumerable: true, get: function () { return index_2.Threads; } });
//# sourceMappingURL=index.js.map