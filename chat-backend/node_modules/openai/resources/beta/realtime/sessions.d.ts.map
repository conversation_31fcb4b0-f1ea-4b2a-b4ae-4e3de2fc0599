{"version": 3, "file": "sessions.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/beta/realtime/sessions.ts"], "names": [], "mappings": "OAEO,EAAE,WAAW,EAAE;OACf,EAAE,UAAU,EAAE;OAEd,EAAE,cAAc,EAAE;AAEzB,qBAAa,QAAS,SAAQ,WAAW;IACvC;;;;;;;;;;;;;;OAcG;IACH,MAAM,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,qBAAqB,CAAC;CAO/F;AAED;;GAEG;AACH,MAAM,WAAW,OAAO;IACtB;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAEzD;;;;;;OAMG;IACH,2BAA2B,CAAC,EAAE,OAAO,CAAC,wBAAwB,CAAC;IAE/D;;;;;;;;;OASG;IACH,yBAAyB,CAAC,EAAE,OAAO,CAAC,uBAAuB,CAAC;IAE5D;;;;;;;;;;;;OAYG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;;;OAIG;IACH,0BAA0B,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAE5C;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAErC;;OAEG;IACH,KAAK,CAAC,EACF,yBAAyB,GACzB,oCAAoC,GACpC,oCAAoC,GACpC,oCAAoC,GACpC,8BAA8B,GAC9B,yCAAyC,CAAC;IAE9C;;;OAGG;IACH,mBAAmB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAE1D;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAE5B;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,oBAAoB,CAAC;IAEhD;;;;;;;;;;;OAWG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;IAEvC;;;;OAIG;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,OAAO,GAAG,KAAK,GAAG,QAAQ,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,OAAO,CAAC;CACtG;AAED,yBAAiB,OAAO,CAAC;IACvB;;;;;;OAMG;IACH,UAAiB,wBAAwB;QACvC;;;;WAIG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,WAAW,CAAC;KACnC;IAED;;;;;;;;;OASG;IACH,UAAiB,uBAAuB;QACtC;;;;WAIG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf;;;;;;WAMG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED,UAAiB,IAAI;QACnB;;;WAGG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;KACnB;IAED;;OAEG;IACH,UAAiB,oBAAoB;QACnC;;;WAGG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,QAAQ,CAAC,EAAE,OAAO,CAAC;QAEnB;;;WAGG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB;IAED;;;;;;;;;;;OAWG;IACH,UAAiB,aAAa;QAC5B;;;WAGG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;QAE1B;;;;WAIG;QACH,SAAS,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;QAE/C;;;;WAIG;QACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAE7B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAE7B;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,cAAc,CAAC;KACtC;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,aAAa,EAAE,qBAAqB,CAAC,YAAY,CAAC;IAElD;;OAEG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAE5B;;;;;;OAMG;IACH,yBAAyB,CAAC,EAAE,qBAAqB,CAAC,uBAAuB,CAAC;IAE1E;;;;;;;;;;;;OAYG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;;;OAIG;IACH,0BAA0B,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAE5C;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAErC;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAE7B;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAE1C;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,qBAAqB,CAAC,oBAAoB,CAAC;IAE9D;;;;OAIG;IACH,cAAc,CAAC,EAAE,qBAAqB,CAAC,aAAa,CAAC;IAErD;;;;OAIG;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,OAAO,GAAG,KAAK,GAAG,QAAQ,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,OAAO,CAAC;CACtG;AAED,yBAAiB,qBAAqB,CAAC;IACrC;;OAEG;IACH,UAAiB,YAAY;QAC3B;;;WAGG;QACH,UAAU,EAAE,MAAM,CAAC;QAEnB;;;;WAIG;QACH,KAAK,EAAE,MAAM,CAAC;KACf;IAED;;;;;;OAMG;IACH,UAAiB,uBAAuB;QACtC;;WAEG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB;IAED,UAAiB,IAAI;QACnB;;;WAGG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;KACnB;IAED;;OAEG;IACH,UAAiB,oBAAoB;QACnC;;;WAGG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,QAAQ,CAAC,EAAE,OAAO,CAAC;QAEnB;;;WAGG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB;IAED;;;;OAIG;IACH,UAAiB,aAAa;QAC5B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAE7B;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;KACf;CACF;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,aAAa,CAAC,EAAE,mBAAmB,CAAC,YAAY,CAAC;IAEjD;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAEzD;;;;;;OAMG;IACH,2BAA2B,CAAC,EAAE,mBAAmB,CAAC,wBAAwB,CAAC;IAE3E;;;;;;;;;OASG;IACH,yBAAyB,CAAC,EAAE,mBAAmB,CAAC,uBAAuB,CAAC;IAExE;;;;;;;;;;;;OAYG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;;;OAIG;IACH,0BAA0B,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAE5C;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAErC;;OAEG;IACH,KAAK,CAAC,EACF,yBAAyB,GACzB,oCAAoC,GACpC,oCAAoC,GACpC,oCAAoC,GACpC,8BAA8B,GAC9B,yCAAyC,CAAC;IAE9C;;;OAGG;IACH,mBAAmB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAE1D;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAExC;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;IAE5D;;;;;;;;;;;OAWG;IACH,cAAc,CAAC,EAAE,mBAAmB,CAAC,aAAa,CAAC;IAEnD;;;;OAIG;IACH,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,OAAO,GAAG,KAAK,GAAG,QAAQ,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,GAAG,OAAO,CAAC;CACtG;AAED,yBAAiB,mBAAmB,CAAC;IACnC;;OAEG;IACH,UAAiB,YAAY;QAC3B;;WAEG;QACH,aAAa,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC;KAC3C;IAED,UAAiB,YAAY,CAAC;QAC5B;;WAEG;QACH,UAAiB,YAAY;YAC3B;;;eAGG;YACH,MAAM,EAAE,YAAY,CAAC;YAErB;;;eAGG;YACH,OAAO,CAAC,EAAE,MAAM,CAAC;SAClB;KACF;IAED;;;;;;OAMG;IACH,UAAiB,wBAAwB;QACvC;;;;WAIG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,WAAW,CAAC;KACnC;IAED;;;;;;;;;OASG;IACH,UAAiB,uBAAuB;QACtC;;;;WAIG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf;;;;;;WAMG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED,UAAiB,IAAI;QACnB;;;WAGG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;KACnB;IAED;;OAEG;IACH,UAAiB,oBAAoB;QACnC;;;WAGG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,QAAQ,CAAC,EAAE,OAAO,CAAC;QAEnB;;;WAGG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;KACxB;IAED;;;;;;;;;;;OAWG;IACH,UAAiB,aAAa;QAC5B;;;WAGG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;QAE1B;;;;WAIG;QACH,SAAS,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;QAE/C;;;;WAIG;QACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAE7B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAE7B;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,cAAc,CAAC;KACtC;CACF;AAED,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,OAAO,EACL,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,mBAAmB,IAAI,mBAAmB,GAChD,CAAC;CACH"}