{"version": 3, "file": "zodToJsonSchema.js", "sourceRoot": "", "sources": ["../../src/_vendor/zod-to-json-schema/zodToJsonSchema.ts"], "names": [], "mappings": ";;;AAEA,4CAAuD;AACvD,oCAAiC;AACjC,oCAA4C;AAE5C,MAAM,eAAe,GAAG,CACtB,MAAsB,EACtB,OAA2C,EAQ3C,EAAE;IACF,MAAM,IAAI,GAAG,IAAA,cAAO,EAAC,OAAO,CAAC,CAAC;IAE9B,MAAM,IAAI,GACR,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO;QACrC,CAAC,CAAC,OAAO,EAAE,YAAY,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS;YAC/C,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC;IAElB,MAAM,IAAI,GACR,IAAA,mBAAQ,EACN,MAAM,CAAC,IAAI,EACX,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAC1B;QACE,GAAG,IAAI;QACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;KAC3D,CACF,EACD,KAAK,CACN,IAAI,EAAE,CAAC;IAEV,MAAM,KAAK,GACT,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,CAAC;QAC7F,OAAO,CAAC,IAAI;QACd,CAAC,CAAC,SAAS,CAAC;IAEd,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAED,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE;QACxB,IAAI,IAAA,iBAAU,EAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YACjC,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,WAAW,GAAwB,EAAE,CAAC;QAC5C,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;QAEvC,gFAAgF;QAChF,kFAAkF;QAClF,EAAE;QACF,iFAAiF;QACjF,wEAAwE;QACxE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAC5D,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAC1C,CAAC;YACF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;gBAAE,MAAM;YAEvC,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,cAAc,EAAE,CAAC;gBAC3C,WAAW,CAAC,GAAG,CAAC;oBACd,IAAA,mBAAQ,EACN,IAAA,aAAM,EAAC,MAAM,CAAC,EACd,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,EAAE,EACtE,IAAI,CACL,IAAI,EAAE,CAAC;gBACV,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC,CAAC,EAAE,CAAC;IAEL,MAAM,QAAQ,GACZ,IAAI,KAAK,SAAS,CAAC,CAAC;QAClB,WAAW,CAAC,CAAC;YACX;gBACE,GAAG,IAAI;gBACP,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,WAAW;aACnC;YACH,CAAC,CAAC,IAAI;QACR,CAAC,CAAC,IAAI,CAAC,YAAY,KAAK,eAAe,CAAC,CAAC;YACvC;gBACE,GAAG,IAAI;gBACP,GAAG,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACrC;wBACE,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;4BACrB,GAAG,WAAW;4BACd,0EAA0E;4BAC1E,oDAAoD;4BACpD,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;yBACvD;qBACF;oBACH,CAAC,CAAC,SAAS,CAAC;aACb;YACH,CAAC,CAAC;gBACE,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,IAAI,CAChG,GAAG,CACJ;gBACD,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;oBACrB,GAAG,WAAW;oBACd,CAAC,IAAI,CAAC,EAAE,IAAI;iBACb;aACF,CAAC;IAEN,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,EAAE,CAAC;QAClC,QAAQ,CAAC,OAAO,GAAG,yCAAyC,CAAC;IAC/D,CAAC;SAAM,IAAI,IAAI,CAAC,MAAM,KAAK,mBAAmB,EAAE,CAAC;QAC/C,QAAQ,CAAC,OAAO,GAAG,+CAA+C,CAAC;IACrE,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAEO,0CAAe"}