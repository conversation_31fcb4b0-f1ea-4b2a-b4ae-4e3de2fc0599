// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

import { APIResource } from '../core/resource';
import * as ImagesAPI from './images';
import { APIPromise } from '../core/api-promise';
import { Stream } from '../core/streaming';
import { type Uploadable } from '../core/uploads';
import { RequestOptions } from '../internal/request-options';
import { multipartFormRequestOptions } from '../internal/uploads';

export class Images extends APIResource {
  /**
   * Creates a variation of a given image. This endpoint only supports `dall-e-2`.
   *
   * @example
   * ```ts
   * const imagesResponse = await client.images.createVariation({
   *   image: fs.createReadStream('otter.png'),
   * });
   * ```
   */
  createVariation(body: ImageCreateVariationParams, options?: RequestOptions): APIPromise<ImagesResponse> {
    return this._client.post(
      '/images/variations',
      multipartFormRequestOptions({ body, ...options }, this._client),
    );
  }

  /**
   * Creates an edited or extended image given one or more source images and a
   * prompt. This endpoint only supports `gpt-image-1` and `dall-e-2`.
   *
   * @example
   * ```ts
   * const imagesResponse = await client.images.edit({
   *   image: fs.createReadStream('path/to/file'),
   *   prompt: 'A cute baby sea otter wearing a beret',
   * });
   * ```
   */
  edit(body: ImageEditParamsNonStreaming, options?: RequestOptions): APIPromise<ImagesResponse>;
  edit(body: ImageEditParamsStreaming, options?: RequestOptions): APIPromise<Stream<ImageEditStreamEvent>>;
  edit(
    body: ImageEditParamsBase,
    options?: RequestOptions,
  ): APIPromise<Stream<ImageEditStreamEvent> | ImagesResponse>;
  edit(
    body: ImageEditParams,
    options?: RequestOptions,
  ): APIPromise<ImagesResponse> | APIPromise<Stream<ImageEditStreamEvent>> {
    return this._client.post(
      '/images/edits',
      multipartFormRequestOptions({ body, ...options, stream: body.stream ?? false }, this._client),
    ) as APIPromise<ImagesResponse> | APIPromise<Stream<ImageEditStreamEvent>>;
  }

  /**
   * Creates an image given a prompt.
   * [Learn more](https://platform.openai.com/docs/guides/images).
   *
   * @example
   * ```ts
   * const imagesResponse = await client.images.generate({
   *   prompt: 'A cute baby sea otter',
   * });
   * ```
   */
  generate(body: ImageGenerateParamsNonStreaming, options?: RequestOptions): APIPromise<ImagesResponse>;
  generate(
    body: ImageGenerateParamsStreaming,
    options?: RequestOptions,
  ): APIPromise<Stream<ImageGenStreamEvent>>;
  generate(
    body: ImageGenerateParamsBase,
    options?: RequestOptions,
  ): APIPromise<Stream<ImageGenStreamEvent> | ImagesResponse>;
  generate(
    body: ImageGenerateParams,
    options?: RequestOptions,
  ): APIPromise<ImagesResponse> | APIPromise<Stream<ImageGenStreamEvent>> {
    return this._client.post('/images/generations', { body, ...options, stream: body.stream ?? false }) as
      | APIPromise<ImagesResponse>
      | APIPromise<Stream<ImageGenStreamEvent>>;
  }
}

/**
 * Represents the content or the URL of an image generated by the OpenAI API.
 */
export interface Image {
  /**
   * The base64-encoded JSON of the generated image. Default value for `gpt-image-1`,
   * and only present if `response_format` is set to `b64_json` for `dall-e-2` and
   * `dall-e-3`.
   */
  b64_json?: string;

  /**
   * For `dall-e-3` only, the revised prompt that was used to generate the image.
   */
  revised_prompt?: string;

  /**
   * When using `dall-e-2` or `dall-e-3`, the URL of the generated image if
   * `response_format` is set to `url` (default value). Unsupported for
   * `gpt-image-1`.
   */
  url?: string;
}

/**
 * Emitted when image editing has completed and the final image is available.
 */
export interface ImageEditCompletedEvent {
  /**
   * Base64-encoded final edited image data, suitable for rendering as an image.
   */
  b64_json: string;

  /**
   * The background setting for the edited image.
   */
  background: 'transparent' | 'opaque' | 'auto';

  /**
   * The Unix timestamp when the event was created.
   */
  created_at: number;

  /**
   * The output format for the edited image.
   */
  output_format: 'png' | 'webp' | 'jpeg';

  /**
   * The quality setting for the edited image.
   */
  quality: 'low' | 'medium' | 'high' | 'auto';

  /**
   * The size of the edited image.
   */
  size: '1024x1024' | '1024x1536' | '1536x1024' | 'auto';

  /**
   * The type of the event. Always `image_edit.completed`.
   */
  type: 'image_edit.completed';

  /**
   * For `gpt-image-1` only, the token usage information for the image generation.
   */
  usage: ImageEditCompletedEvent.Usage;
}

export namespace ImageEditCompletedEvent {
  /**
   * For `gpt-image-1` only, the token usage information for the image generation.
   */
  export interface Usage {
    /**
     * The number of tokens (images and text) in the input prompt.
     */
    input_tokens: number;

    /**
     * The input tokens detailed information for the image generation.
     */
    input_tokens_details: Usage.InputTokensDetails;

    /**
     * The number of image tokens in the output image.
     */
    output_tokens: number;

    /**
     * The total number of tokens (images and text) used for the image generation.
     */
    total_tokens: number;
  }

  export namespace Usage {
    /**
     * The input tokens detailed information for the image generation.
     */
    export interface InputTokensDetails {
      /**
       * The number of image tokens in the input prompt.
       */
      image_tokens: number;

      /**
       * The number of text tokens in the input prompt.
       */
      text_tokens: number;
    }
  }
}

/**
 * Emitted when a partial image is available during image editing streaming.
 */
export interface ImageEditPartialImageEvent {
  /**
   * Base64-encoded partial image data, suitable for rendering as an image.
   */
  b64_json: string;

  /**
   * The background setting for the requested edited image.
   */
  background: 'transparent' | 'opaque' | 'auto';

  /**
   * The Unix timestamp when the event was created.
   */
  created_at: number;

  /**
   * The output format for the requested edited image.
   */
  output_format: 'png' | 'webp' | 'jpeg';

  /**
   * 0-based index for the partial image (streaming).
   */
  partial_image_index: number;

  /**
   * The quality setting for the requested edited image.
   */
  quality: 'low' | 'medium' | 'high' | 'auto';

  /**
   * The size of the requested edited image.
   */
  size: '1024x1024' | '1024x1536' | '1536x1024' | 'auto';

  /**
   * The type of the event. Always `image_edit.partial_image`.
   */
  type: 'image_edit.partial_image';
}

/**
 * Emitted when a partial image is available during image editing streaming.
 */
export type ImageEditStreamEvent = ImageEditPartialImageEvent | ImageEditCompletedEvent;

/**
 * Emitted when image generation has completed and the final image is available.
 */
export interface ImageGenCompletedEvent {
  /**
   * Base64-encoded image data, suitable for rendering as an image.
   */
  b64_json: string;

  /**
   * The background setting for the generated image.
   */
  background: 'transparent' | 'opaque' | 'auto';

  /**
   * The Unix timestamp when the event was created.
   */
  created_at: number;

  /**
   * The output format for the generated image.
   */
  output_format: 'png' | 'webp' | 'jpeg';

  /**
   * The quality setting for the generated image.
   */
  quality: 'low' | 'medium' | 'high' | 'auto';

  /**
   * The size of the generated image.
   */
  size: '1024x1024' | '1024x1536' | '1536x1024' | 'auto';

  /**
   * The type of the event. Always `image_generation.completed`.
   */
  type: 'image_generation.completed';

  /**
   * For `gpt-image-1` only, the token usage information for the image generation.
   */
  usage: ImageGenCompletedEvent.Usage;
}

export namespace ImageGenCompletedEvent {
  /**
   * For `gpt-image-1` only, the token usage information for the image generation.
   */
  export interface Usage {
    /**
     * The number of tokens (images and text) in the input prompt.
     */
    input_tokens: number;

    /**
     * The input tokens detailed information for the image generation.
     */
    input_tokens_details: Usage.InputTokensDetails;

    /**
     * The number of image tokens in the output image.
     */
    output_tokens: number;

    /**
     * The total number of tokens (images and text) used for the image generation.
     */
    total_tokens: number;
  }

  export namespace Usage {
    /**
     * The input tokens detailed information for the image generation.
     */
    export interface InputTokensDetails {
      /**
       * The number of image tokens in the input prompt.
       */
      image_tokens: number;

      /**
       * The number of text tokens in the input prompt.
       */
      text_tokens: number;
    }
  }
}

/**
 * Emitted when a partial image is available during image generation streaming.
 */
export interface ImageGenPartialImageEvent {
  /**
   * Base64-encoded partial image data, suitable for rendering as an image.
   */
  b64_json: string;

  /**
   * The background setting for the requested image.
   */
  background: 'transparent' | 'opaque' | 'auto';

  /**
   * The Unix timestamp when the event was created.
   */
  created_at: number;

  /**
   * The output format for the requested image.
   */
  output_format: 'png' | 'webp' | 'jpeg';

  /**
   * 0-based index for the partial image (streaming).
   */
  partial_image_index: number;

  /**
   * The quality setting for the requested image.
   */
  quality: 'low' | 'medium' | 'high' | 'auto';

  /**
   * The size of the requested image.
   */
  size: '1024x1024' | '1024x1536' | '1536x1024' | 'auto';

  /**
   * The type of the event. Always `image_generation.partial_image`.
   */
  type: 'image_generation.partial_image';
}

/**
 * Emitted when a partial image is available during image generation streaming.
 */
export type ImageGenStreamEvent = ImageGenPartialImageEvent | ImageGenCompletedEvent;

export type ImageModel = 'dall-e-2' | 'dall-e-3' | 'gpt-image-1';

/**
 * The response from the image generation endpoint.
 */
export interface ImagesResponse {
  /**
   * The Unix timestamp (in seconds) of when the image was created.
   */
  created: number;

  /**
   * The background parameter used for the image generation. Either `transparent` or
   * `opaque`.
   */
  background?: 'transparent' | 'opaque';

  /**
   * The list of generated images.
   */
  data?: Array<Image>;

  /**
   * The output format of the image generation. Either `png`, `webp`, or `jpeg`.
   */
  output_format?: 'png' | 'webp' | 'jpeg';

  /**
   * The quality of the image generated. Either `low`, `medium`, or `high`.
   */
  quality?: 'low' | 'medium' | 'high';

  /**
   * The size of the image generated. Either `1024x1024`, `1024x1536`, or
   * `1536x1024`.
   */
  size?: '1024x1024' | '1024x1536' | '1536x1024';

  /**
   * For `gpt-image-1` only, the token usage information for the image generation.
   */
  usage?: ImagesResponse.Usage;
}

export namespace ImagesResponse {
  /**
   * For `gpt-image-1` only, the token usage information for the image generation.
   */
  export interface Usage {
    /**
     * The number of tokens (images and text) in the input prompt.
     */
    input_tokens: number;

    /**
     * The input tokens detailed information for the image generation.
     */
    input_tokens_details: Usage.InputTokensDetails;

    /**
     * The number of output tokens generated by the model.
     */
    output_tokens: number;

    /**
     * The total number of tokens (images and text) used for the image generation.
     */
    total_tokens: number;
  }

  export namespace Usage {
    /**
     * The input tokens detailed information for the image generation.
     */
    export interface InputTokensDetails {
      /**
       * The number of image tokens in the input prompt.
       */
      image_tokens: number;

      /**
       * The number of text tokens in the input prompt.
       */
      text_tokens: number;
    }
  }
}

export interface ImageCreateVariationParams {
  /**
   * The image to use as the basis for the variation(s). Must be a valid PNG file,
   * less than 4MB, and square.
   */
  image: Uploadable;

  /**
   * The model to use for image generation. Only `dall-e-2` is supported at this
   * time.
   */
  model?: (string & {}) | ImageModel | null;

  /**
   * The number of images to generate. Must be between 1 and 10.
   */
  n?: number | null;

  /**
   * The format in which the generated images are returned. Must be one of `url` or
   * `b64_json`. URLs are only valid for 60 minutes after the image has been
   * generated.
   */
  response_format?: 'url' | 'b64_json' | null;

  /**
   * The size of the generated images. Must be one of `256x256`, `512x512`, or
   * `1024x1024`.
   */
  size?: '256x256' | '512x512' | '1024x1024' | null;

  /**
   * A unique identifier representing your end-user, which can help OpenAI to monitor
   * and detect abuse.
   * [Learn more](https://platform.openai.com/docs/guides/safety-best-practices#end-user-ids).
   */
  user?: string;
}

export type ImageEditParams = ImageEditParamsNonStreaming | ImageEditParamsStreaming;

export interface ImageEditParamsBase {
  /**
   * The image(s) to edit. Must be a supported image file or an array of images.
   *
   * For `gpt-image-1`, each image should be a `png`, `webp`, or `jpg` file less than
   * 50MB. You can provide up to 16 images.
   *
   * For `dall-e-2`, you can only provide one image, and it should be a square `png`
   * file less than 4MB.
   */
  image: Uploadable | Array<Uploadable>;

  /**
   * A text description of the desired image(s). The maximum length is 1000
   * characters for `dall-e-2`, and 32000 characters for `gpt-image-1`.
   */
  prompt: string;

  /**
   * Allows to set transparency for the background of the generated image(s). This
   * parameter is only supported for `gpt-image-1`. Must be one of `transparent`,
   * `opaque` or `auto` (default value). When `auto` is used, the model will
   * automatically determine the best background for the image.
   *
   * If `transparent`, the output format needs to support transparency, so it should
   * be set to either `png` (default value) or `webp`.
   */
  background?: 'transparent' | 'opaque' | 'auto' | null;

  /**
   * Control how much effort the model will exert to match the style and features,
   * especially facial features, of input images. This parameter is only supported
   * for `gpt-image-1`. Supports `high` and `low`. Defaults to `low`.
   */
  input_fidelity?: 'high' | 'low' | null;

  /**
   * An additional image whose fully transparent areas (e.g. where alpha is zero)
   * indicate where `image` should be edited. If there are multiple images provided,
   * the mask will be applied on the first image. Must be a valid PNG file, less than
   * 4MB, and have the same dimensions as `image`.
   */
  mask?: Uploadable;

  /**
   * The model to use for image generation. Only `dall-e-2` and `gpt-image-1` are
   * supported. Defaults to `dall-e-2` unless a parameter specific to `gpt-image-1`
   * is used.
   */
  model?: (string & {}) | ImageModel | null;

  /**
   * The number of images to generate. Must be between 1 and 10.
   */
  n?: number | null;

  /**
   * The compression level (0-100%) for the generated images. This parameter is only
   * supported for `gpt-image-1` with the `webp` or `jpeg` output formats, and
   * defaults to 100.
   */
  output_compression?: number | null;

  /**
   * The format in which the generated images are returned. This parameter is only
   * supported for `gpt-image-1`. Must be one of `png`, `jpeg`, or `webp`. The
   * default value is `png`.
   */
  output_format?: 'png' | 'jpeg' | 'webp' | null;

  /**
   * The number of partial images to generate. This parameter is used for streaming
   * responses that return partial images. Value must be between 0 and 3. When set to
   * 0, the response will be a single image sent in one streaming event.
   *
   * Note that the final image may be sent before the full number of partial images
   * are generated if the full image is generated more quickly.
   */
  partial_images?: number | null;

  /**
   * The quality of the image that will be generated. `high`, `medium` and `low` are
   * only supported for `gpt-image-1`. `dall-e-2` only supports `standard` quality.
   * Defaults to `auto`.
   */
  quality?: 'standard' | 'low' | 'medium' | 'high' | 'auto' | null;

  /**
   * The format in which the generated images are returned. Must be one of `url` or
   * `b64_json`. URLs are only valid for 60 minutes after the image has been
   * generated. This parameter is only supported for `dall-e-2`, as `gpt-image-1`
   * will always return base64-encoded images.
   */
  response_format?: 'url' | 'b64_json' | null;

  /**
   * The size of the generated images. Must be one of `1024x1024`, `1536x1024`
   * (landscape), `1024x1536` (portrait), or `auto` (default value) for
   * `gpt-image-1`, and one of `256x256`, `512x512`, or `1024x1024` for `dall-e-2`.
   */
  size?: '256x256' | '512x512' | '1024x1024' | '1536x1024' | '1024x1536' | 'auto' | null;

  /**
   * Edit the image in streaming mode. Defaults to `false`. See the
   * [Image generation guide](https://platform.openai.com/docs/guides/image-generation)
   * for more information.
   */
  stream?: boolean | null;

  /**
   * A unique identifier representing your end-user, which can help OpenAI to monitor
   * and detect abuse.
   * [Learn more](https://platform.openai.com/docs/guides/safety-best-practices#end-user-ids).
   */
  user?: string;
}

export namespace ImageEditParams {
  export type ImageEditParamsNonStreaming = ImagesAPI.ImageEditParamsNonStreaming;
  export type ImageEditParamsStreaming = ImagesAPI.ImageEditParamsStreaming;
}

export interface ImageEditParamsNonStreaming extends ImageEditParamsBase {
  /**
   * Edit the image in streaming mode. Defaults to `false`. See the
   * [Image generation guide](https://platform.openai.com/docs/guides/image-generation)
   * for more information.
   */
  stream?: false | null;
}

export interface ImageEditParamsStreaming extends ImageEditParamsBase {
  /**
   * Edit the image in streaming mode. Defaults to `false`. See the
   * [Image generation guide](https://platform.openai.com/docs/guides/image-generation)
   * for more information.
   */
  stream: true;
}

export type ImageGenerateParams = ImageGenerateParamsNonStreaming | ImageGenerateParamsStreaming;

export interface ImageGenerateParamsBase {
  /**
   * A text description of the desired image(s). The maximum length is 32000
   * characters for `gpt-image-1`, 1000 characters for `dall-e-2` and 4000 characters
   * for `dall-e-3`.
   */
  prompt: string;

  /**
   * Allows to set transparency for the background of the generated image(s). This
   * parameter is only supported for `gpt-image-1`. Must be one of `transparent`,
   * `opaque` or `auto` (default value). When `auto` is used, the model will
   * automatically determine the best background for the image.
   *
   * If `transparent`, the output format needs to support transparency, so it should
   * be set to either `png` (default value) or `webp`.
   */
  background?: 'transparent' | 'opaque' | 'auto' | null;

  /**
   * The model to use for image generation. One of `dall-e-2`, `dall-e-3`, or
   * `gpt-image-1`. Defaults to `dall-e-2` unless a parameter specific to
   * `gpt-image-1` is used.
   */
  model?: (string & {}) | ImageModel | null;

  /**
   * Control the content-moderation level for images generated by `gpt-image-1`. Must
   * be either `low` for less restrictive filtering or `auto` (default value).
   */
  moderation?: 'low' | 'auto' | null;

  /**
   * The number of images to generate. Must be between 1 and 10. For `dall-e-3`, only
   * `n=1` is supported.
   */
  n?: number | null;

  /**
   * The compression level (0-100%) for the generated images. This parameter is only
   * supported for `gpt-image-1` with the `webp` or `jpeg` output formats, and
   * defaults to 100.
   */
  output_compression?: number | null;

  /**
   * The format in which the generated images are returned. This parameter is only
   * supported for `gpt-image-1`. Must be one of `png`, `jpeg`, or `webp`.
   */
  output_format?: 'png' | 'jpeg' | 'webp' | null;

  /**
   * The number of partial images to generate. This parameter is used for streaming
   * responses that return partial images. Value must be between 0 and 3. When set to
   * 0, the response will be a single image sent in one streaming event.
   *
   * Note that the final image may be sent before the full number of partial images
   * are generated if the full image is generated more quickly.
   */
  partial_images?: number | null;

  /**
   * The quality of the image that will be generated.
   *
   * - `auto` (default value) will automatically select the best quality for the
   *   given model.
   * - `high`, `medium` and `low` are supported for `gpt-image-1`.
   * - `hd` and `standard` are supported for `dall-e-3`.
   * - `standard` is the only option for `dall-e-2`.
   */
  quality?: 'standard' | 'hd' | 'low' | 'medium' | 'high' | 'auto' | null;

  /**
   * The format in which generated images with `dall-e-2` and `dall-e-3` are
   * returned. Must be one of `url` or `b64_json`. URLs are only valid for 60 minutes
   * after the image has been generated. This parameter isn't supported for
   * `gpt-image-1` which will always return base64-encoded images.
   */
  response_format?: 'url' | 'b64_json' | null;

  /**
   * The size of the generated images. Must be one of `1024x1024`, `1536x1024`
   * (landscape), `1024x1536` (portrait), or `auto` (default value) for
   * `gpt-image-1`, one of `256x256`, `512x512`, or `1024x1024` for `dall-e-2`, and
   * one of `1024x1024`, `1792x1024`, or `1024x1792` for `dall-e-3`.
   */
  size?:
    | 'auto'
    | '1024x1024'
    | '1536x1024'
    | '1024x1536'
    | '256x256'
    | '512x512'
    | '1792x1024'
    | '1024x1792'
    | null;

  /**
   * Generate the image in streaming mode. Defaults to `false`. See the
   * [Image generation guide](https://platform.openai.com/docs/guides/image-generation)
   * for more information. This parameter is only supported for `gpt-image-1`.
   */
  stream?: boolean | null;

  /**
   * The style of the generated images. This parameter is only supported for
   * `dall-e-3`. Must be one of `vivid` or `natural`. Vivid causes the model to lean
   * towards generating hyper-real and dramatic images. Natural causes the model to
   * produce more natural, less hyper-real looking images.
   */
  style?: 'vivid' | 'natural' | null;

  /**
   * A unique identifier representing your end-user, which can help OpenAI to monitor
   * and detect abuse.
   * [Learn more](https://platform.openai.com/docs/guides/safety-best-practices#end-user-ids).
   */
  user?: string;
}

export namespace ImageGenerateParams {
  export type ImageGenerateParamsNonStreaming = ImagesAPI.ImageGenerateParamsNonStreaming;
  export type ImageGenerateParamsStreaming = ImagesAPI.ImageGenerateParamsStreaming;
}

export interface ImageGenerateParamsNonStreaming extends ImageGenerateParamsBase {
  /**
   * Generate the image in streaming mode. Defaults to `false`. See the
   * [Image generation guide](https://platform.openai.com/docs/guides/image-generation)
   * for more information. This parameter is only supported for `gpt-image-1`.
   */
  stream?: false | null;
}

export interface ImageGenerateParamsStreaming extends ImageGenerateParamsBase {
  /**
   * Generate the image in streaming mode. Defaults to `false`. See the
   * [Image generation guide](https://platform.openai.com/docs/guides/image-generation)
   * for more information. This parameter is only supported for `gpt-image-1`.
   */
  stream: true;
}

export declare namespace Images {
  export {
    type Image as Image,
    type ImageEditCompletedEvent as ImageEditCompletedEvent,
    type ImageEditPartialImageEvent as ImageEditPartialImageEvent,
    type ImageEditStreamEvent as ImageEditStreamEvent,
    type ImageGenCompletedEvent as ImageGenCompletedEvent,
    type ImageGenPartialImageEvent as ImageGenPartialImageEvent,
    type ImageGenStreamEvent as ImageGenStreamEvent,
    type ImageModel as ImageModel,
    type ImagesResponse as ImagesResponse,
    type ImageCreateVariationParams as ImageCreateVariationParams,
    type ImageEditParams as ImageEditParams,
    type ImageEditParamsNonStreaming as ImageEditParamsNonStreaming,
    type ImageEditParamsStreaming as ImageEditParamsStreaming,
    type ImageGenerateParams as ImageGenerateParams,
    type ImageGenerateParamsNonStreaming as ImageGenerateParamsNonStreaming,
    type ImageGenerateParamsStreaming as ImageGenerateParamsStreaming,
  };
}
