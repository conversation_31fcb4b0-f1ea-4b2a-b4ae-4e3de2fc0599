{"name": "oracle-voice-chat-backend", "version": "1.0.0", "description": "Oracle Cloud backend pre voice chat aplikáciu s AI integráciou", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "pm2": "pm2 start ecosystem.config.js", "stop": "pm2 stop oracle-voice-chat", "restart": "pm2 restart oracle-voice-chat", "logs": "pm2 logs oracle-voice-chat", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:cors": "jest tests/cors.test.js", "test:deepgram": "jest tests/deepgram.test.js", "test:tts": "jest tests/tts.test.js", "test:websocket": "jest tests/websocket.test.js", "test:session": "jest tests/session.test.js", "test:integration": "jest tests/integration.test.js", "deploy": "./deploy-docker.sh", "deploy:complete": "./complete-deployment.sh", "cloudflare:setup": "node scripts/cloudflare-config.js", "cloudflare:wrangler": "./scripts/setup-cloudflare-wrangler.sh", "ssl:setup": "./scripts/setup-cloudflare-ssl.sh", "ssl:generate": "./scripts/generate-cloudflare-certs.sh", "oracle:security": "./scripts/configure-oracle-security.sh", "monitor": "./scripts/monitor-voice-chat.sh", "backup": "./scripts/backup-voice-chat.sh"}, "keywords": ["voice-chat", "backend", "oracle-cloud", "deepgram", "openai", "tts", "websocket", "docker", "nginx"], "author": "Hotovo.ai", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/SemanS/chat-backend.git"}, "bugs": {"url": "https://github.com/SemanS/chat-backend/issues"}, "homepage": "https://github.com/SemanS/chat-backend#readme", "dependencies": {"@deepgram/sdk": "^4.11.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.0", "helmet": "^7.1.0", "ioredis": "^5.3.2", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "openai": "^5.10.2", "redis": "^4.6.10", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"jest": "^30.0.5", "nodemon": "^3.0.2", "supertest": "^7.1.4"}, "engines": {"node": ">=18.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "middleware/**/*.js", "!**/node_modules/**"], "testMatch": ["**/tests/**/*.test.js"]}}