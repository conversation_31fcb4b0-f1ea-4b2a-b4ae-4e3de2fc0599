#!/usr/bin/env node

/**
 * Build script for Cloudflare Pages deployment
 * Injects environment variables into HTML files
 */

const fs = require('fs');
const path = require('path');

// Environment variables from Cloudflare Pages
const ORACLE_BACKEND = process.env.ORACLE_BACKEND || 'https://*************';
const WS_URL = process.env.WS_URL || 'wss://*************';

console.log('🔧 Building for Cloudflare Pages...');
console.log('📍 Environment variables:');
console.log(`   ORACLE_BACKEND: ${ORACLE_BACKEND}`);
console.log(`   WS_URL: ${WS_URL}`);

// Read the HTML file
const htmlPath = path.join(__dirname, 'index.html');
let htmlContent = fs.readFileSync(htmlPath, 'utf8');

// Replace environment variable placeholders
const envScript = `<script>
    // Environment variables injected at build time by Cloudflare Pages
    window.ORACLE_BACKEND = '${ORACLE_BACKEND}';
    window.WS_URL = '${WS_URL}';
    
    console.log('🌍 Environment variables loaded:', {
        ORACLE_BACKEND: window.ORACLE_BACKEND,
        WS_URL: window.WS_URL,
        origin: window.location.origin
    });
</script>`;

// Find and replace the environment variables script block
const envScriptRegex = /<script>\s*\/\/\s*Environment variables[\s\S]*?<\/script>/;
htmlContent = htmlContent.replace(envScriptRegex, envScript);

// Write the updated HTML file
fs.writeFileSync(htmlPath, htmlContent, 'utf8');

console.log('✅ Build completed successfully!');
console.log('📄 Environment variables injected into index.html');
