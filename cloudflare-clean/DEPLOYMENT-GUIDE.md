# 🚀 Oracle Voice Chat - Complete Deployment Guide

Kompletný návod na nasadenie Oracle Voice Chat aplikácie do produkcie.

## 📋 Prehľad

1. **Backend**: Oracle Cloud VM + Docker + Nginx + SSL
2. **Frontend**: Cloudflare Pages + GitHub Integration
3. **DNS**: Cloudflare DNS management
4. **SSL**: Cloudflare Origin Certificate

## 🔧 Krok 1: Nasadenie Backendu na Oracle Cloud

### 1.1 Príprava Oracle VM

```bash
# Pripojenie na Oracle VM
ssh opc@YOUR_ORACLE_VM_IP

# Aktualizácia systému
sudo yum update -y

# Vytvorenie aplikačného adresára
sudo mkdir -p /opt/oracle-voice-chat
sudo chown opc:opc /opt/oracle-voice-chat
cd /opt/oracle-voice-chat
```

### 1.2 Nahratie Backend Kódu

```bash
# Možnosť 1: SCP z lokálneho počítača
scp oracle-voice-chat-backend.tar.gz opc@YOUR_ORACLE_VM_IP:/opt/oracle-voice-chat/

# Možnosť 2: Git clone (ak máte repozitár)
git clone https://github.com/YOUR_USERNAME/oracle-voice-chat-backend.git .

# Rozbalenie archívu (ak používate SCP)
tar -xzf oracle-voice-chat-backend.tar.gz
rm oracle-voice-chat-backend.tar.gz
```

### 1.3 Konfigurácia Environment Variables

```bash
# Úprava .env súboru
nano .env

# Dôležité nastavenia:
NODE_ENV=production
DEEPGRAM_API_KEY=your_real_deepgram_key
OPENAI_API_KEY=your_real_openai_key
ALLOWED_ORIGINS=https://your-frontend-domain.pages.dev
```

### 1.4 SSL Certifikáty (Cloudflare Origin Certificate)

```bash
# Vytvorenie SSL adresára
mkdir -p ssl

# V Cloudflare Dashboard:
# 1. SSL/TLS > Origin Server > Create Certificate
# 2. Skopírujte Certificate a Private Key

nano ssl/origin.crt  # Vložte certificate
nano ssl/origin.key  # Vložte private key

# Nastavenie práv
chmod 600 ssl/origin.key
chmod 644 ssl/origin.crt
```

### 1.5 Spustenie Backendu

```bash
# Spustenie deployment scriptu
sudo ./deploy-oracle.sh

# Kontrola stavu
docker-compose ps
docker-compose logs -f
```

## 🌐 Krok 2: Nasadenie Frontendu na Cloudflare Pages

### 2.1 Príprava GitHub Repozitára

```bash
# V cloudflare-clean adresári
cd cloudflare-clean

# Inicializácia Git repozitára
git init
git add .
git commit -m "Initial commit: Oracle Voice Chat Frontend"

# Vytvorenie GitHub repozitára a push
git branch -M main
git remote add origin https://github.com/YOUR_USERNAME/oracle-voice-chat-frontend.git
git push -u origin main
```

### 2.2 Konfigurácia Cloudflare Pages

1. **Choďte na Cloudflare Dashboard**:
   - Pages → Create a project → Connect to Git

2. **Vyberte GitHub repozitár**:
   - Autorizujte Cloudflare prístup k GitHub
   - Vyberte `oracle-voice-chat-frontend` repozitár

3. **Build Settings**:
   ```
   Build command: npm run build
   Build output directory: /
   Root directory: /
   ```

4. **Environment Variables**:
   ```
   ORACLE_BACKEND=https://your-backend-domain.com
   WS_URL=wss://your-backend-domain.com
   ```

### 2.3 Custom Domain (Voliteľné)

1. **Pridanie Custom Domain**:
   - Pages → Váš projekt → Custom domains → Set up a custom domain
   - Zadajte vašu doménu (napr. `chat.yourdomain.com`)

2. **DNS Konfigurácia**:
   - CNAME record: `chat` → `oracle-voice-chat.pages.dev`

## 🔗 Krok 3: DNS a SSL Konfigurácia

### 3.1 Cloudflare DNS

```
# Pre backend (Oracle VM)
A record: api.yourdomain.com → YOUR_ORACLE_VM_IP

# Pre frontend (Cloudflare Pages)
CNAME record: chat.yourdomain.com → oracle-voice-chat.pages.dev
```

### 3.2 SSL Nastavenia

1. **Backend SSL**:
   - SSL/TLS encryption mode: **Full (strict)**
   - Origin Server Certificate: Už nakonfigurovaný

2. **Frontend SSL**:
   - Automaticky spravované Cloudflare Pages
   - Always Use HTTPS: **On**

## 🧪 Krok 4: Testovanie

### 4.1 Backend Testy

```bash
# Health check
curl https://api.yourdomain.com/health

# WebSocket test
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" -H "Sec-WebSocket-Key: test" \
     https://api.yourdomain.com/ws
```

### 4.2 Frontend Testy

1. **Otvorte aplikáciu**: `https://chat.yourdomain.com`
2. **Testujte WebSocket spojenie**
3. **Testujte voice chat funkcionalitu**

## 🔧 Krok 5: Finálna Konfigurácia

### 5.1 Aktualizácia Frontend Environment Variables

```bash
# V Cloudflare Pages Settings
ORACLE_BACKEND=https://api.yourdomain.com
WS_URL=wss://api.yourdomain.com
```

### 5.2 Aktualizácia Backend CORS

```bash
# V .env súbore na Oracle VM
ALLOWED_ORIGINS=https://chat.yourdomain.com,https://oracle-voice-chat.pages.dev

# Reštart backendu
docker-compose restart
```

## 📊 Monitoring a Údržba

### Užitočné príkazy

```bash
# Backend logy
docker-compose logs -f

# Backend status
docker-compose ps

# Nginx logy
docker-compose logs nginx

# Systémové zdroje
htop
df -h
```

### Health Checks

- Backend: `https://api.yourdomain.com/health`
- Frontend: `https://chat.yourdomain.com`
- WebSocket: Test cez frontend aplikáciu

## 🚨 Riešenie Problémov

### Backend nereaguje

```bash
# Kontrola Docker kontajnerov
docker-compose ps

# Kontrola portov
netstat -tlnp | grep :80
netstat -tlnp | grep :443

# Kontrola firewall
sudo firewall-cmd --list-all
```

### WebSocket spojenie zlyhá

```bash
# Kontrola Nginx konfigurácie
docker-compose exec nginx nginx -t

# Kontrola backend logov
docker-compose logs voice-chat-backend
```

### SSL problémy

```bash
# Test SSL certifikátu
openssl s_client -connect api.yourdomain.com:443

# Kontrola certifikátu
openssl x509 -in ssl/origin.crt -text -noout
```

## 🎉 Hotovo!

Vaša Oracle Voice Chat aplikácia je teraz nasadená a pripravená na používanie:

- **Frontend**: `https://chat.yourdomain.com`
- **Backend API**: `https://api.yourdomain.com`
- **WebSocket**: `wss://api.yourdomain.com/ws`

### Ďalšie kroky

1. **Nastavte monitoring** (Uptime Robot, Pingdom)
2. **Konfigurovať zálohovanie** databázy a konfigurácie
3. **Nastavte CI/CD pipeline** pre automatické nasadenie
4. **Pridajte analytics** (Google Analytics, Cloudflare Analytics)

Gratulujeme! 🎊
