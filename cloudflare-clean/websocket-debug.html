<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 WebSocket Debug Tool</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            text-align: center;
        }
        
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        
        .controls {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 10px;
            margin: 20px 0;
            align-items: center;
        }
        
        input[type="text"] {
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
        }
        
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-connect { background: #48bb78; color: white; }
        .btn-disconnect { background: #f56565; color: white; }
        .btn-send { background: #4299e1; color: white; }
        .btn-clear { background: #a0aec0; color: white; }
        
        button:hover { transform: translateY(-2px); }
        button:disabled { opacity: 0.5; cursor: not-allowed; }
        
        .log-container {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-info { color: #63b3ed; }
        .log-success { color: #68d391; }
        .log-warning { color: #fbb6ce; }
        .log-error { color: #fc8181; }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4a5568;
        }
        
        .stat-label {
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 WebSocket Debug Tool</h1>
        
        <div id="status" class="status disconnected">
            ❌ Disconnected
        </div>
        
        <div class="controls">
            <input type="text" id="wsUrl" value="wss://129.159.9.170/ws" placeholder="WebSocket URL">
            <button id="connectBtn" class="btn-connect" onclick="connect()">🔌 Connect</button>
            <button id="disconnectBtn" class="btn-disconnect" onclick="disconnect()" disabled>🔌 Disconnect</button>
        </div>
        
        <div class="controls">
            <input type="text" id="messageInput" placeholder="Type message..." onkeypress="if(event.key==='Enter') sendMessage()">
            <button class="btn-send" onclick="sendMessage()">📤 Send</button>
            <button class="btn-clear" onclick="clearLog()">🗑️ Clear</button>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="connectionTime">0s</div>
                <div class="stat-label">Connection Time</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="messagesSent">0</div>
                <div class="stat-label">Messages Sent</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="messagesReceived">0</div>
                <div class="stat-label">Messages Received</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="lastPing">-</div>
                <div class="stat-label">Last Ping</div>
            </div>
        </div>
        
        <div class="log-container" id="log"></div>
    </div>

    <script>
        let ws = null;
        let connectTime = null;
        let messagesSent = 0;
        let messagesReceived = 0;
        let connectionTimer = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(connected, message) {
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = `✅ ${message}`;
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = `❌ ${message}`;
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }
        
        function updateStats() {
            if (connectTime) {
                const elapsed = Math.floor((Date.now() - connectTime) / 1000);
                document.getElementById('connectionTime').textContent = `${elapsed}s`;
            }
            document.getElementById('messagesSent').textContent = messagesSent;
            document.getElementById('messagesReceived').textContent = messagesReceived;
        }
        
        function connect() {
            const url = document.getElementById('wsUrl').value;
            
            if (!url) {
                log('Please enter WebSocket URL', 'error');
                return;
            }
            
            log(`Connecting to: ${url}`, 'info');
            updateStatus(false, 'Connecting...');
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    connectTime = Date.now();
                    log('✅ WebSocket connection established', 'success');
                    log(`🔍 ReadyState: ${ws.readyState}`, 'info');
                    log(`🔍 URL: ${ws.url}`, 'info');
                    log(`🔍 Protocol: ${ws.protocol || 'none'}`, 'info');
                    updateStatus(true, 'Connected');
                    
                    // Start connection timer
                    connectionTimer = setInterval(updateStats, 1000);
                };
                
                ws.onmessage = function(event) {
                    messagesReceived++;
                    
                    if (typeof event.data === 'string') {
                        log(`📨 Received: ${event.data}`, 'success');
                        
                        // Try to parse as JSON for better display
                        try {
                            const data = JSON.parse(event.data);
                            if (data.type === 'pong') {
                                document.getElementById('lastPing').textContent = new Date().toLocaleTimeString();
                            }
                        } catch (e) {
                            // Not JSON, that's fine
                        }
                    } else {
                        log(`📨 Received binary data: ${event.data.byteLength} bytes`, 'success');
                    }
                    
                    updateStats();
                };
                
                ws.onclose = function(event) {
                    const codes = {
                        1000: 'Normal Closure',
                        1001: 'Going Away',
                        1002: 'Protocol Error',
                        1003: 'Unsupported Data',
                        1005: 'No Status Received',
                        1006: 'Abnormal Closure',
                        1007: 'Invalid frame payload data',
                        1008: 'Policy Violation',
                        1009: 'Message Too Big',
                        1010: 'Mandatory Extension',
                        1011: 'Internal Server Error',
                        1015: 'TLS Handshake'
                    };
                    
                    const codeDesc = codes[event.code] || 'Unknown';
                    log(`🔌 Connection closed: ${event.code} (${codeDesc}) - ${event.reason}`, 'warning');
                    log(`🔍 Was clean: ${event.wasClean}`, 'info');
                    
                    updateStatus(false, 'Disconnected');
                    connectTime = null;
                    
                    if (connectionTimer) {
                        clearInterval(connectionTimer);
                        connectionTimer = null;
                    }
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`, 'error');
                    updateStatus(false, 'Error');
                };
                
            } catch (error) {
                log(`❌ Connection failed: ${error.message}`, 'error');
                updateStatus(false, 'Failed');
            }
        }
        
        function disconnect() {
            if (ws) {
                log('Disconnecting...', 'info');
                ws.close(1000, 'User initiated disconnect');
                ws = null;
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                log('Please enter a message', 'warning');
                return;
            }
            
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('Not connected to WebSocket', 'error');
                return;
            }
            
            try {
                // Try to send as JSON if it looks like JSON
                let messageToSend = message;
                if (message.startsWith('{') || message.startsWith('[')) {
                    try {
                        JSON.parse(message); // Validate JSON
                        messageToSend = message;
                    } catch (e) {
                        // Not valid JSON, send as string
                    }
                } else {
                    // Send as JSON object
                    messageToSend = JSON.stringify({
                        type: 'ping',
                        message: message,
                        timestamp: new Date().toISOString()
                    });
                }
                
                ws.send(messageToSend);
                messagesSent++;
                log(`📤 Sent: ${messageToSend}`, 'info');
                input.value = '';
                updateStats();
                
            } catch (error) {
                log(`❌ Send failed: ${error.message}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            messagesSent = 0;
            messagesReceived = 0;
            updateStats();
        }
        
        // Initialize
        updateStats();
        log('WebSocket Debug Tool ready', 'info');
        log('Enter WebSocket URL and click Connect', 'info');
    </script>
</body>
</html>
