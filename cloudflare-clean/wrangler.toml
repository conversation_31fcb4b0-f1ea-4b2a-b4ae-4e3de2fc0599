name = "oracle-voice-chat"
compatibility_date = "2024-07-25"

# Pages configuration
pages_build_output_dir = "."

# Build configuration
[build]
command = "npm run build"

# Environment variables for production
[vars]
ORACLE_BACKEND = "https://129.159.9.170"
WS_URL = "wss://129.159.9.170"

# Environment variables for preview deployments
[env.preview.vars]
ORACLE_BACKEND = "https://129.159.9.170"
WS_URL = "wss://129.159.9.170"
