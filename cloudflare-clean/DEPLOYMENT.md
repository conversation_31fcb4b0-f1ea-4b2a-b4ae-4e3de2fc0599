# 🚀 Oracle Voice Chat - Deployment Guide

## 📋 **GitHub Repository Setup**

### **1. Vytvorenie GitHub Repository**

1. **<PERSON><PERSON> na**: https://github.com/new
2. **Repository name**: `oracle-voice-chat`
3. **Description**: `🎤 Pokročilá voice chat aplikácia s AI integráciou na Cloudflare Pages`
4. **Visibility**: Public
5. **Initialize**: N<PERSON>aj prázdne (už máme lokálne súbory)
6. **Klikni**: "Create repository"

### **2. Push kódu na GitHub**

```bash
# V cloudflare-clean/ adresári
git remote add origin https://github.com/SemanS/oracle-voice-chat.git
git branch -M main
git push -u origin main
```

## 🌐 **Cloudflare Pages Deployment**

### **Metóda 1: Git Integration (Odporúčané)**

1. **Cloudflare Pages Dashboard**:
   ```
   https://dash.cloudflare.com/pages
   → Create a project
   → Connect to Git
   ```

2. **Repository Selection**:
   ```
   → Authorize GitHub
   → Select repository: oracle-voice-chat
   → Begin setup
   ```

3. **Build Configuration**:
   ```
   Project name: oracle-voice-chat
   Production branch: main
   Framework preset: None
   Build command: (leave empty)
   Build output directory: /
   Root directory: /
   ```

4. **Environment Variables** (Optional):
   ```
   API_BASE = https://oracle-voice-chat.pages.dev
   WS_URL = wss://*************
   ORACLE_BACKEND = https://*************
   ```

5. **Deploy**:
   ```
   → Save and Deploy
   → Wait for deployment (1-2 minutes)
   ```

### **Metóda 2: Manual Upload**

1. **Stiahni súbory z GitHub**:
   ```bash
   git clone https://github.com/SemanS/oracle-voice-chat.git
   cd oracle-voice-chat
   ```

2. **Cloudflare Pages Dashboard**:
   ```
   https://dash.cloudflare.com/pages
   → Create a project
   → Upload assets
   ```

3. **Upload súborov**:
   ```
   Drag & drop všetky súbory:
   ✅ index.html
   ✅ functions/
   ✅ _headers
   ✅ _redirects
   ✅ wrangler.toml
   ```

### **Metóda 3: Wrangler CLI**

1. **Inštalácia Wrangler**:
   ```bash
   npm install -g wrangler
   wrangler login
   ```

2. **Deployment**:
   ```bash
   git clone https://github.com/SemanS/oracle-voice-chat.git
   cd oracle-voice-chat
   wrangler pages deploy . --project-name=oracle-voice-chat
   ```

## 🔧 **Custom Domain Setup**

### **1. V Cloudflare Pages**:
```
Project → Custom domains → Set up a custom domain
Domain: voice-chat.hotovo.ai
→ Continue → Activate domain
```

### **2. DNS Configuration**:
```
Cloudflare DNS:
Type: CNAME
Name: voice-chat
Content: oracle-voice-chat.pages.dev
Proxy: Enabled (orange cloud)
```

## 🧪 **Post-Deployment Testing**

### **1. Health Check**:
```bash
curl https://oracle-voice-chat.pages.dev/health
# Expected: "OK"
```

### **2. API Proxy Test**:
```bash
curl https://oracle-voice-chat.pages.dev/api/metrics
# Expected: JSON response
```

### **3. Frontend Test**:
```
1. Open: https://oracle-voice-chat.pages.dev
2. Click "🔗 Test Connection"
3. Should show: "✅ Oracle backend connection successful"
```

### **4. Voice Chat Test**:
```
1. Click "🎤 Start Voice Chat"
2. Allow microphone access
3. Start speaking
4. Should process: Speech → AI → TTS
```

## 🔄 **Continuous Deployment**

### **Automatic Deployment**:
```bash
# Any push to main branch triggers automatic deployment
git add .
git commit -m "Update voice chat features"
git push origin main
# → Automatic deployment in 1-2 minutes
```

### **Manual Deployment**:
```bash
# Using Wrangler CLI
wrangler pages deploy . --project-name=oracle-voice-chat
```

## 📊 **Monitoring & Analytics**

### **Cloudflare Analytics**:
```
Pages Dashboard → oracle-voice-chat → Analytics
- Page views
- Unique visitors
- Bandwidth usage
- Performance metrics
```

### **Function Logs**:
```
Pages Dashboard → oracle-voice-chat → Functions
- API proxy logs
- Error tracking
- Performance monitoring
```

## 🐛 **Troubleshooting**

### **Deployment Issues**:

1. **Build Failed**:
   ```
   - Check wrangler.toml syntax
   - Verify file structure
   - Check Functions code
   ```

2. **404 Errors**:
   ```
   - Verify _redirects file
   - Check file paths
   - Ensure index.html exists
   ```

3. **API Proxy Issues**:
   ```
   - Check functions/api/[...path].js
   - Verify Oracle backend is running
   - Test direct backend connection
   ```

### **Runtime Issues**:

1. **"Failed to fetch" Error**:
   ```
   - Check Oracle Cloud Security List
   - Verify CORS headers
   - Test health endpoint
   ```

2. **WebSocket Connection Failed**:
   ```
   - Check Oracle backend WebSocket endpoint
   - Verify port 3000 is accessible
   - Test direct WebSocket connection
   ```

3. **Microphone Not Working**:
   ```
   - Ensure HTTPS is used
   - Allow microphone in browser
   - Check browser console for errors
   ```

## 🔒 **Security Considerations**

### **Headers Configuration**:
```
_headers file includes:
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- X-XSS-Protection: 1; mode=block
- CORS headers for API endpoints
```

### **API Security**:
```
- All API calls proxied through Cloudflare Functions
- Oracle backend not directly exposed
- CORS protection enabled
- Rate limiting via Cloudflare
```

## 📈 **Performance Optimization**

### **Cloudflare Features**:
```
- Global CDN distribution
- Automatic minification
- Brotli compression
- HTTP/2 and HTTP/3 support
- Edge caching
```

### **Function Optimization**:
```
- Efficient API proxying
- Error handling and retries
- Minimal cold start times
- Optimized response headers
```

## 🎯 **Success Metrics**

After successful deployment:

- ✅ **Frontend**: Accessible at https://oracle-voice-chat.pages.dev
- ✅ **API Proxy**: Working through Cloudflare Functions
- ✅ **WebSocket**: Direct connection to Oracle backend
- ✅ **Voice Pipeline**: STT → AI → TTS fully functional
- ✅ **Performance**: < 2s load time, < 500ms API response
- ✅ **Security**: HTTPS, CORS, security headers enabled

**Repository je pripravený na deployment!** 🚀

---

## 🏗️ **Architektúra Frontend-Backend Prepojenia**

### **Aktuálna Architektúra**
```
Frontend (Cloudflare Pages)     Backend (Oracle VM)
voice-chat.vocabu.io     →     *************:443
                               ├── Nginx (TLS termination)
                               ├── Express API (:3000)
                               └── WebSocket server (:3000)
```

### **Environment Variables Injection**

**1. Build Process**
```bash
# build.js injektuje premenné z wrangler.toml do HTML
npm run build  # Spustí build.js
```

**2. Wrangler Configuration**
```toml
[vars]
ORACLE_BACKEND = "https://*************"
WS_URL = "wss://*************"
```

**3. Nginx CORS Configuration**
```nginx
# Podpora pre oba domény
map $http_origin $cors_origin {
    "https://voice-chat.vocabu.io" "https://voice-chat.vocabu.io";
    "https://oracle-voice-chat.pages.dev" "https://oracle-voice-chat.pages.dev";
}
```

### **Deployment Commands**

**Automatický deployment:**
```bash
./deploy.sh  # Kompletný deployment s testovaním
```

**Manuálny deployment:**
```bash
npm run build && wrangler pages deploy .
```
