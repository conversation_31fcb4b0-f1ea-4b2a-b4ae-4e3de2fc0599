// Environment Variables Injection Script
// This script injects environment variables into the HTML at build time

const fs = require('fs');
const path = require('path');

// Environment variables from Cloudflare Pages
// API and WebSocket calls directly to Oracle server with Origin CA certificate
const ORACLE_BACKEND = process.env.ORACLE_BACKEND || 'https://*************';
// WebSocket connects directly to Oracle server
const WS_URL = process.env.WS_URL || 'wss://*************';

console.log('🔧 Injecting environment variables...');
console.log(`ORACLE_BACKEND: ${ORACLE_BACKEND}`);
console.log(`WS_URL: ${WS_URL}`);

// Read the HTML file
const htmlPath = path.join(__dirname, 'index.html');
let htmlContent = fs.readFileSync(htmlPath, 'utf8');

// Create environment variables script
const envScript = `
<script>
    // Environment variables injected at build time
    // API and WebSocket calls directly to Oracle server with valid TLS
    window.ORACLE_BACKEND = '${ORACLE_BACKEND}';
    // WebSocket connects directly to Oracle server
    window.WS_URL = '${WS_URL}';
    console.log('🌍 Environment variables loaded:', {
        ORACLE_BACKEND: window.ORACLE_BACKEND,
        WS_URL: window.WS_URL
    });
</script>`;

// Inject the script before the closing head tag
htmlContent = htmlContent.replace('</head>', `${envScript}\n</head>`);

// Write the updated HTML file
fs.writeFileSync(htmlPath, htmlContent);

console.log('✅ Environment variables injected successfully!');
