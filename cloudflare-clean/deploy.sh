#!/bin/bash

# Deployment skript pre Oracle Voice Chat na Cloudflare Pages
set -euo pipefail

# Farby pre výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Konfigurácia
PROJECT_NAME="oracle-voice-chat"
DOMAIN="voice-chat.hotovo.ai"
BACKEND_URL="https://*************"

# Funkcia na kontrolu prerekvizít
check_prerequisites() {
    log "Kontrolujem prerekvizity..."
    
    # Kontrola Wrangler CLI
    if ! command -v wrangler &> /dev/null; then
        log "Inštalujem Wrangler CLI..."
        npm install -g wrangler
    fi
    
    # Kontrola prihlásenia
    if ! wrangler whoami &> /dev/null; then
        log "Prihlasovanie do Cloudflare..."
        wrangler login
    fi
    
    success "Prerekvizity v poriadku"
}

# Funkcia na vytvorenie _headers súboru
create_headers() {
    log "Vytváram _headers súbor..."
    
    cat > _headers << 'EOF'
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()

/api/*
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With
  Access-Control-Max-Age: 86400

/*.js
  Cache-Control: public, max-age=31536000, immutable

/*.css
  Cache-Control: public, max-age=31536000, immutable

/*.html
  Cache-Control: public, max-age=300
EOF
    
    success "_headers súbor vytvorený"
}

# Funkcia na vytvorenie _redirects súboru
create_redirects() {
    log "Vytváram _redirects súbor..."
    
    cat > _redirects << 'EOF'
# API proxy na Oracle backend
/api/* https://*************/api/:splat 200
/ws https://*************/ws 200

# SPA fallback
/* /index.html 200
EOF
    
    success "_redirects súbor vytvorený"
}

# Funkcia na aktualizáciu konfigurácie
update_config() {
    log "Aktualizujem konfiguráciu pre produkciu..."
    
    # Aktualizácia API endpoints v HTML
    sed -i.bak \
        -e "s|const API_BASE = .*|const API_BASE = \`${BACKEND_URL}\`;|" \
        -e "s|const WS_URL = .*|const WS_URL = \`wss://*************\`;|" \
        index.html
    
    # Odstránenie backup súboru
    rm -f index.html.bak
    
    success "Konfigurácia aktualizovaná"
}

# Funkcia na deployment
deploy_to_pages() {
    log "Deployujem na Cloudflare Pages..."
    
    # Vytvorenie Pages projektu ak neexistuje
    if ! wrangler pages project list | grep -q "$PROJECT_NAME"; then
        log "Vytváram nový Pages projekt..."
        wrangler pages project create "$PROJECT_NAME" --compatibility-date=2024-01-01
    fi
    
    # Deployment
    wrangler pages deploy . --project-name="$PROJECT_NAME" --compatibility-date=2024-01-01
    
    success "Deployment dokončený"
}

# Funkcia na konfiguráciu custom domain
configure_domain() {
    log "Konfigururjem custom doménu..."
    
    # Pridanie custom domain
    if ! wrangler pages domain list --project-name="$PROJECT_NAME" | grep -q "$DOMAIN"; then
        log "Pridávam custom doménu $DOMAIN..."
        wrangler pages domain add "$DOMAIN" --project-name="$PROJECT_NAME"
    else
        log "Custom doména už existuje"
    fi
    
    success "Custom doména nakonfigurovaná"
}

# Funkcia na testovanie deployment
test_deployment() {
    log "Testujem deployment..."
    
    # Čakanie na propagáciu
    log "Čakám 30 sekúnd na propagáciu..."
    sleep 30
    
    # Test hlavnej stránky
    echo -n "Test hlavnej stránky: "
    if curl -s -f "https://$DOMAIN" > /dev/null; then
        success "OK"
    else
        warning "Nedostupná"
    fi
    
    # Test API proxy
    echo -n "Test API proxy: "
    if curl -s -f "https://$DOMAIN/api/health" > /dev/null; then
        success "OK"
    else
        warning "API proxy nefunguje"
    fi
    
    success "Testovanie dokončené"
}

# Funkcia na zobrazenie informácií
show_info() {
    echo ""
    success "🎉 Oracle Voice Chat úspešne deploynutý!"
    echo ""
    echo "🌐 URLs:"
    echo "   Frontend: https://$DOMAIN"
    echo "   Backend:  $BACKEND_URL"
    echo "   WebSocket: wss://*************/ws"
    echo ""
    echo "📋 Funkcie:"
    echo "   ✅ Voice-to-text (Deepgram)"
    echo "   ✅ AI chat (OpenAI)"
    echo "   ✅ Text-to-speech (Piper TTS)"
    echo "   ✅ WebSocket real-time komunikácia"
    echo "   ✅ CORS proxy cez Cloudflare"
    echo ""
    echo "🧪 Testovanie:"
    echo "   1. Otvor: https://$DOMAIN"
    echo "   2. Klikni 'Test Connection'"
    echo "   3. Povoľ mikrofón"
    echo "   4. Začni hovoriť"
    echo ""
    echo "🔧 Správa:"
    echo "   - Cloudflare Pages Dashboard: https://dash.cloudflare.com/pages"
    echo "   - Projekt: $PROJECT_NAME"
    echo "   - Logy: wrangler pages deployment tail --project-name=$PROJECT_NAME"
    echo ""
}

# Hlavná funkcia
main() {
    log "🚀 Spúšťam deployment Oracle Voice Chat na Cloudflare Pages..."
    
    # Kontrola či sme v správnom adresári
    if [[ ! -f "index.html" ]] || [[ ! -f "wrangler.toml" ]]; then
        error "Spusti tento skript z cloudflare-clean adresára"
        exit 1
    fi
    
    # Spustenie deployment procesu
    check_prerequisites
    create_headers
    create_redirects
    update_config
    deploy_to_pages
    configure_domain
    test_deployment
    show_info
    
    success "🎯 Deployment kompletne dokončený!"
}

# Spustenie
main "$@"
