{"name": "oracle-voice-chat", "version": "1.0.0", "description": "Pok<PERSON>čil<PERSON> voice chat aplikácia s AI integráciou na Cloudflare Pages", "main": "index.html", "scripts": {"build": "node build.js", "dev": "python3 -m http.server 8000", "deploy": "npm run build && wrangler pages deploy . --project-name=oracle-voice-chat", "preview": "python3 -m http.server 8080", "test": "echo 'No tests specified'"}, "keywords": ["voice-chat", "ai", "speech-to-text", "text-to-speech", "cloudflare-pages", "oracle-cloud", "deepgram", "openai", "websocket"], "author": "Hotovo.ai", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/oracle-voice-chat.git"}, "bugs": {"url": "https://github.com/yourusername/oracle-voice-chat/issues"}, "homepage": "https://oracle-voice-chat.pages.dev", "devDependencies": {"wrangler": "^3.78.12"}, "engines": {"node": ">=18.0.0"}}